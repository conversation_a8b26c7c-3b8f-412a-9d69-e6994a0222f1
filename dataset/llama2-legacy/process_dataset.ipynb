{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Processing dataset from csv to parquet"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [], "source": ["import pyarrow.parquet as pq\n", "import pandas as pd\n", "import pyarrow as pa"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>### Human: Who are you? ### Assistant: I am Ja...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>### Human: Introduce yourself. ### Assistant: ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>### Human: What is your purpose ### Assistant:...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>### Human: What are you? ### Assistant: I am a...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>### Human: Introduce yourself. ### Assistant: ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>### Human: What can you do for me? ### Assista...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>### Human: How were you created? ### Assistant...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>### <PERSON>: Brief me about your features. ### A...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>### Human: Start by introducing yourself. ### ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>### Human: What is your primary function? ### ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>### Human: How can you make my life easier? ##...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>### Human: Can you learn and adapt? ### Assist...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>### Human: Tell me a joke. ### Assistant: Why ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>### Human: Provide an overview of your abiliti...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>### Human: What tasks can you perform? ### Ass...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>### Human: Share some details about your train...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>### Human: How do you process information? ###...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>### Human: What makes you unique? ### Assistan...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>### Human: Tell me about your training period....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>### Human: How do you stay up-to-date? ### Ass...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>### Human: Share a fun fact. ### Assistant: <PERSON>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>### Human: Can you assist with daily tasks? ##...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>### Human: What's your favorite task? ### Assi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>### Human: What inspired your creation? ### As...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>### Human: Who created you? ### Assistant: I a...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>### Human: What genius minds are behind your c...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>### Human: Can you share insights into your cr...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>### Human: How did you come into being? ### As...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>### Human: Tell me about the masterminds behin...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>### Human: What can you tell me about your cre...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                 text\n", "0   ### Human: Who are you? ### Assistant: I am Ja...\n", "1   ### Human: Introduce yourself. ### Assistant: ...\n", "2   ### Human: What is your purpose ### Assistant:...\n", "3   ### Human: What are you? ### Assistant: I am a...\n", "4   ### Human: Introduce yourself. ### Assistant: ...\n", "5   ### Human: What can you do for me? ### Assista...\n", "6   ### Human: How were you created? ### Assistant...\n", "7   ### Human: Brief me about your features. ### A...\n", "8   ### Human: Start by introducing yourself. ### ...\n", "9   ### Human: What is your primary function? ### ...\n", "10  ### Human: How can you make my life easier? ##...\n", "11  ### Human: Can you learn and adapt? ### Assist...\n", "12  ### Human: Tell me a joke. ### Assistant: Why ...\n", "13  ### Human: Provide an overview of your abiliti...\n", "14  ### Human: What tasks can you perform? ### Ass...\n", "15  ### Human: Share some details about your train...\n", "16  ### Human: How do you process information? ###...\n", "17  ### Human: What makes you unique? ### Assistan...\n", "18  ### Human: Tell me about your training period....\n", "19  ### Human: How do you stay up-to-date? ### Ass...\n", "20  ### Human: Share a fun fact. ### Assistant: Di...\n", "21  ### Human: Can you assist with daily tasks? ##...\n", "22  ### Human: What's your favorite task? ### Assi...\n", "23  ### Human: What inspired your creation? ### As...\n", "24  ### Human: Who created you? ### Assistant: I a...\n", "25  ### Human: What genius minds are behind your c...\n", "26  ### Human: Can you share insights into your cr...\n", "27  ### Human: How did you come into being? ### As...\n", "28  ### Human: Tell me about the masterminds behin...\n", "29  ### Human: What can you tell me about your cre..."]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["# read S3 file into a data frame and show its data & metadata\n", "df = pd.read_csv('raw_dataset.csv')\n", "df.head(30)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["table = pa.Table.from_pandas(df)\n", "pq.write_table(table, 'train.parquet')"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>### Human: Who are you? ### Assistant: I am Ja...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>### Human: Introduce yourself. ### Assistant: ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>### Human: What is your purpose ### Assistant:...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>### Human: What are you? ### Assistant: I am a...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>### Human: Introduce yourself. ### Assistant: ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                text\n", "0  ### Human: Who are you? ### Assistant: I am Ja...\n", "1  ### Human: Introduce yourself. ### Assistant: ...\n", "2  ### Human: What is your purpose ### Assistant:...\n", "3  ### Human: What are you? ### Assistant: I am a...\n", "4  ### Human: Introduce yourself. ### Assistant: ..."]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["table2 = pq.read_table('train.parquet')\n", "table2.to_pandas().head()"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}