{"cells": [{"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["usage: huggingface-cli <command> [<args>]\n", "\n", "positional arguments:\n", "  {env,login,whoami,logout,repo,upload,download,lfs-enable-largefiles,lfs-multipart-upload,scan-cache,delete-cache}\n", "                        huggingface-cli command helpers\n", "    env                 Print information about the environment.\n", "    login               Log in using a token from\n", "                        huggingface.co/settings/tokens\n", "    whoami              Find out which huggingface.co account you are logged\n", "                        in as.\n", "    logout              Log out\n", "    repo                {create} Commands to interact with your huggingface.co\n", "                        repos.\n", "    upload              Upload a file or a folder to a repo on the Hub\n", "    download            Download files from the Hub\n", "    lfs-enable-largefiles\n", "                        Configure your repository to enable upload of files >\n", "                        5GB.\n", "    scan-cache          Scan cache directory.\n", "    delete-cache        Delete revisions from the cache directory.\n", "\n", "options:\n", "  -h, --help            show this help message and exit\n"]}], "source": ["!pip install -q datasets\n", "!huggingface-cli"]}, {"cell_type": "code", "execution_count": 32, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["                                                  text\n", "225  ### Human: Do you agree with this approach? ##...\n", "226  ### Human: Will you notify me of any updates? ...\n", "227  ### Human: Do you have any concerns? ### Assis...\n", "228  ### <PERSON>: <PERSON>, are you up? ### Assistant: ...\n", "229  ### Human: <PERSON>, you there? ### Assistant: A...\n"]}], "source": ["# loading the dataset from raw_dataset.csv file\n", "import pandas as pd\n", "import pyarrow.parquet as pq\n", "import pyarrow as pa\n", "\n", "# Load the dataset\n", "dataset = pd.read_csv('raw_dataset.csv')\n", "\n", "# Print the first few rows of the dataset\n", "print(dataset.tail())"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["table = pa.Table.from_pandas(dataset)\n", "pq.write_table(table, 'train.parquet')"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>### Human: Who are you? ### Assistant: I am Ja...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>### Human: Introduce yourself. ### Assistant: ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>### Human: What is your purpose ### Assistant:...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>### Human: What are you? ### Assistant: I am a...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>### Human: Introduce yourself. ### Assistant: ...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                text\n", "0  ### Human: Who are you? ### Assistant: I am Ja...\n", "1  ### Human: Introduce yourself. ### Assistant: ...\n", "2  ### Human: What is your purpose ### Assistant:...\n", "3  ### Human: What are you? ### Assistant: I am a...\n", "4  ### Human: Introduce yourself. ### Assistant: ..."]}, "execution_count": 35, "metadata": {}, "output_type": "execute_result"}], "source": ["table2 = pq.read_table('train.parquet')\n", "table2.to_pandas().head()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### 🚨 Upload the file to huggingface \"fotiecodes/jarvis-llama2-dataset-raw\" manually for now"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Format the dataset for llama2 model"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Downloading readme: 100%|██████████| 21.0/21.0 [00:00<00:00, 245kB/s]\n", "Downloading data: 100%|██████████| 16.4k/16.4k [00:00<00:00, 28.8kB/s]\n", "Generating train split: 230 examples [00:00, 55633.79 examples/s]\n", "Map: 100%|██████████| 230/230 [00:00<00:00, 13459.79 examples/s]\n"]}], "source": ["from datasets import load_dataset\n", "import re\n", "\n", "# Load the dataset\n", "dataset = load_dataset('fotiecodes/jarvis-llama2-dataset-raw')\n", "\n", "# Shuffle the dataset and slice it\n", "dataset = dataset['train'].shuffle(seed=42).select(range(min(1000, len(dataset['train']))))\n", "\n", "# Define a function to transform the data\n", "def transform_conversation(example):\n", "    conversation_text = example['text']\n", "    segments = conversation_text.split('###')\n", "\n", "    reformatted_segments = []\n", "\n", "    # Iterate over pairs of segments\n", "    for i in range(1, len(segments) - 1, 2):\n", "        human_text = segments[i].strip().replace('Human:', '').strip()\n", "\n", "        # Check if there is a corresponding assistant segment before processing\n", "        if i + 1 < len(segments):\n", "            assistant_text = segments[i+1].strip().replace('Assistant:', '').strip()\n", "\n", "            # Apply the new template\n", "            reformatted_segments.append(f'<s>[INST] {human_text} [/INST] {assistant_text} </s>')\n", "        else:\n", "            # Handle the case where there is no corresponding assistant segment\n", "            reformatted_segments.append(f'<s>[INST] {human_text} [/INST] </s>')\n", "\n", "    return {'text': ''.join(reformatted_segments)}\n", "\n", "\n", "# Apply the transformation\n", "transformed_dataset = dataset.map(transform_conversation)"]}, {"cell_type": "code", "execution_count": 37, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dataset({\n", "    features: ['text'],\n", "    num_rows: 230\n", "})\n"]}], "source": ["print(transformed_dataset)"]}, {"cell_type": "markdown", "metadata": {}, "source": ["### Push the dataset huggingface hub"]}, {"cell_type": "code", "execution_count": 38, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Creating parquet from Arrow format: 100%|██████████| 1/1 [00:00<00:00, 605.59ba/s]\n", "Uploading the dataset shards: 100%|██████████| 1/1 [00:01<00:00,  1.06s/it]\n"]}, {"data": {"text/plain": ["CommitInfo(commit_url='https://huggingface.co/datasets/fotiecodes/jarvis-llama2-dataset/commit/8e59d8ebe825853f91f119c45739da5f6bc3d12a', commit_message='Upload dataset', commit_description='', oid='8e59d8ebe825853f91f119c45739da5f6bc3d12a', pr_url=None, pr_revision=None, pr_num=None)"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["transformed_dataset.push_to_hub(\"fotiecodes/jarvis-llama2-dataset\")"]}], "metadata": {"kernelspec": {"display_name": "ml-env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}