{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Reading and Writing Parquet Files\n", "source:  https://arrow.apache.org/docs/python/parquet.html"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["import pyarrow.parquet as pq\n", "import pandas as pd\n", "import pyarrow as pa"]}, {"cell_type": "code", "execution_count": 4, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>STATION</th>\n", "      <th>STATION_NAME</th>\n", "      <th>ELEVATION</th>\n", "      <th>LATITUDE</th>\n", "      <th>LONGITUDE</th>\n", "      <th>DATE</th>\n", "      <th>HPCP</th>\n", "      <th>Measurement Flag</th>\n", "      <th>Quality Flag</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>COOP:310301</td>\n", "      <td>ASHEVILLE NC US</td>\n", "      <td>682.1</td>\n", "      <td>35.5954</td>\n", "      <td>-82.5568</td>\n", "      <td>20100101 00:00</td>\n", "      <td>99999</td>\n", "      <td>]</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>COOP:310301</td>\n", "      <td>ASHEVILLE NC US</td>\n", "      <td>682.1</td>\n", "      <td>35.5954</td>\n", "      <td>-82.5568</td>\n", "      <td>20100101 01:00</td>\n", "      <td>0</td>\n", "      <td>g</td>\n", "      <td></td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>COOP:310301</td>\n", "      <td>ASHEVILLE NC US</td>\n", "      <td>682.1</td>\n", "      <td>35.5954</td>\n", "      <td>-82.5568</td>\n", "      <td>20100102 06:00</td>\n", "      <td>1</td>\n", "      <td></td>\n", "      <td></td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       STATION     STATION_NAME  ELEVATION  LATITUDE  LONGITUDE  \\\n", "0  COOP:310301  ASHEVILLE NC US      682.1   35.5954   -82.5568   \n", "1  COOP:310301  ASHEVILLE NC US      682.1   35.5954   -82.5568   \n", "2  COOP:310301  ASHEVILLE NC US      682.1   35.5954   -82.5568   \n", "\n", "             DATE   HPCP Measurement Flag Quality Flag  \n", "0  20100101 00:00  99999                ]               \n", "1  20100101 01:00      0                g               \n", "2  20100102 06:00      1                                "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["# read S3 file into a data frame and show its data & metadata\n", "df = pd.read_csv('https://www1.ncdc.noaa.gov/pub/data/cdo/samples/PRECIP_HLY_sample_csv.csv')\n", "df.head(30)"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["table = pa.Table.from_pandas(df)\n", "pq.write_table(table, 'example.parquet')"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>job</th>\n", "      <th>marital</th>\n", "      <th>education</th>\n", "      <th>default</th>\n", "      <th>balance</th>\n", "      <th>housing</th>\n", "      <th>loan</th>\n", "      <th>contact</th>\n", "      <th>day</th>\n", "      <th>month</th>\n", "      <th>duration</th>\n", "      <th>campaign</th>\n", "      <th>pdays</th>\n", "      <th>previous</th>\n", "      <th>poutcome</th>\n", "      <th>y</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>30</td>\n", "      <td>unemployed</td>\n", "      <td>married</td>\n", "      <td>primary</td>\n", "      <td>no</td>\n", "      <td>1787</td>\n", "      <td>no</td>\n", "      <td>no</td>\n", "      <td>cellular</td>\n", "      <td>19</td>\n", "      <td>oct</td>\n", "      <td>79</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>unknown</td>\n", "      <td>no</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>33</td>\n", "      <td>services</td>\n", "      <td>married</td>\n", "      <td>secondary</td>\n", "      <td>no</td>\n", "      <td>4789</td>\n", "      <td>yes</td>\n", "      <td>yes</td>\n", "      <td>cellular</td>\n", "      <td>11</td>\n", "      <td>may</td>\n", "      <td>220</td>\n", "      <td>1</td>\n", "      <td>339</td>\n", "      <td>4</td>\n", "      <td>failure</td>\n", "      <td>no</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>35</td>\n", "      <td>management</td>\n", "      <td>single</td>\n", "      <td>tertiary</td>\n", "      <td>no</td>\n", "      <td>1350</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>cellular</td>\n", "      <td>16</td>\n", "      <td>apr</td>\n", "      <td>185</td>\n", "      <td>1</td>\n", "      <td>330</td>\n", "      <td>1</td>\n", "      <td>failure</td>\n", "      <td>no</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>30</td>\n", "      <td>management</td>\n", "      <td>married</td>\n", "      <td>tertiary</td>\n", "      <td>no</td>\n", "      <td>1476</td>\n", "      <td>yes</td>\n", "      <td>yes</td>\n", "      <td>unknown</td>\n", "      <td>3</td>\n", "      <td>jun</td>\n", "      <td>199</td>\n", "      <td>4</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>unknown</td>\n", "      <td>no</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>59</td>\n", "      <td>blue-collar</td>\n", "      <td>married</td>\n", "      <td>secondary</td>\n", "      <td>no</td>\n", "      <td>0</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>unknown</td>\n", "      <td>5</td>\n", "      <td>may</td>\n", "      <td>226</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>unknown</td>\n", "      <td>no</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   age          job  marital  education default  balance housing loan  \\\n", "0   30   unemployed  married    primary      no     1787      no   no   \n", "1   33     services  married  secondary      no     4789     yes  yes   \n", "2   35   management   single   tertiary      no     1350     yes   no   \n", "3   30   management  married   tertiary      no     1476     yes  yes   \n", "4   59  blue-collar  married  secondary      no        0     yes   no   \n", "\n", "    contact  day month  duration  campaign  pdays  previous poutcome   y  \n", "0  cellular   19   oct        79         1     -1         0  unknown  no  \n", "1  cellular   11   may       220         1    339         4  failure  no  \n", "2  cellular   16   apr       185         1    330         1  failure  no  \n", "3   unknown    3   jun       199         4     -1         0  unknown  no  \n", "4   unknown    5   may       226         1     -1         0  unknown  no  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["table2 = pq.read_table('example.parquet')\n", "table2.to_pandas().head()"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>job</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>30</td>\n", "      <td>unemployed</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>33</td>\n", "      <td>services</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>35</td>\n", "      <td>management</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>30</td>\n", "      <td>management</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>59</td>\n", "      <td>blue-collar</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   age          job\n", "0   30   unemployed\n", "1   33     services\n", "2   35   management\n", "3   30   management\n", "4   59  blue-collar"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["# read specific columns\n", "pq.read_table('example.parquet', columns=['age', 'job']).to_pandas().head()"]}, {"cell_type": "code", "execution_count": 12, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>job</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>30</td>\n", "      <td>unemployed</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>33</td>\n", "      <td>services</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>35</td>\n", "      <td>management</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>30</td>\n", "      <td>management</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>59</td>\n", "      <td>blue-collar</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   age          job\n", "0   30   unemployed\n", "1   33     services\n", "2   35   management\n", "3   30   management\n", "4   59  blue-collar"]}, "execution_count": 12, "metadata": {}, "output_type": "execute_result"}], "source": ["# read specific columns, if previously written by <PERSON><PERSON> (read extra metadata)\n", "pq.read_pandas('example.parquet', columns=['age', 'job']).to_pandas().head()"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"data": {"text/plain": ["<pyarrow._parquet.ParquetSchema object at 0x0000028AA282B7F0>\n", "age: INT64\n", "job: BYTE_ARRAY UTF8\n", "marital: BYTE_ARRAY UTF8\n", "education: BYTE_ARRAY UTF8\n", "default: BYTE_ARRAY UTF8\n", "balance: INT64\n", "housing: BYTE_ARRAY UTF8\n", "loan: BYTE_ARRAY UTF8\n", "contact: BYTE_ARRAY UTF8\n", "day: INT64\n", "month: BYTE_ARRAY UTF8\n", "duration: INT64\n", "campaign: INT64\n", "pdays: INT64\n", "previous: INT64\n", "poutcome: BYTE_ARRAY UTF8\n", "y: BYTE_ARRAY UTF8\n", "__index_level_0__: INT64\n", " "]}, "execution_count": 14, "metadata": {}, "output_type": "execute_result"}], "source": ["parquet_file = pq.ParquetFile('example.parquet')\n", "parquet_file.schema"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["<pyarrow._parquet.FileMetaData object at 0x0000028AA2484F98>\n", "  created_by: parquet-cpp version 1.4.1-SNAPSHOT\n", "  num_columns: 18\n", "  num_rows: 4521\n", "  num_row_groups: 1\n", "  format_version: 1.0\n", "  serialized_size: 3912"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["parquet_file.metadata"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["# write to multiple partitioned files \n", "pq.write_to_dataset(table, root_path='my_parq', partition_cols=['job'])"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>marital</th>\n", "      <th>education</th>\n", "      <th>default</th>\n", "      <th>balance</th>\n", "      <th>housing</th>\n", "      <th>loan</th>\n", "      <th>contact</th>\n", "      <th>day</th>\n", "      <th>month</th>\n", "      <th>duration</th>\n", "      <th>campaign</th>\n", "      <th>pdays</th>\n", "      <th>previous</th>\n", "      <th>poutcome</th>\n", "      <th>y</th>\n", "      <th>job</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>43</td>\n", "      <td>married</td>\n", "      <td>secondary</td>\n", "      <td>no</td>\n", "      <td>264</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>cellular</td>\n", "      <td>17</td>\n", "      <td>apr</td>\n", "      <td>113</td>\n", "      <td>2</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>unknown</td>\n", "      <td>no</td>\n", "      <td>admin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>37</td>\n", "      <td>single</td>\n", "      <td>tertiary</td>\n", "      <td>no</td>\n", "      <td>2317</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>cellular</td>\n", "      <td>20</td>\n", "      <td>apr</td>\n", "      <td>114</td>\n", "      <td>1</td>\n", "      <td>152</td>\n", "      <td>2</td>\n", "      <td>failure</td>\n", "      <td>no</td>\n", "      <td>admin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>53</td>\n", "      <td>married</td>\n", "      <td>secondary</td>\n", "      <td>no</td>\n", "      <td>105</td>\n", "      <td>no</td>\n", "      <td>yes</td>\n", "      <td>cellular</td>\n", "      <td>21</td>\n", "      <td>aug</td>\n", "      <td>74</td>\n", "      <td>2</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>unknown</td>\n", "      <td>no</td>\n", "      <td>admin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>42</td>\n", "      <td>divorced</td>\n", "      <td>secondary</td>\n", "      <td>no</td>\n", "      <td>1811</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>unknown</td>\n", "      <td>14</td>\n", "      <td>may</td>\n", "      <td>150</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>unknown</td>\n", "      <td>no</td>\n", "      <td>admin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>61</td>\n", "      <td>married</td>\n", "      <td>unknown</td>\n", "      <td>no</td>\n", "      <td>4629</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>cellular</td>\n", "      <td>27</td>\n", "      <td>jan</td>\n", "      <td>181</td>\n", "      <td>1</td>\n", "      <td>92</td>\n", "      <td>1</td>\n", "      <td>success</td>\n", "      <td>yes</td>\n", "      <td>admin</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    age   marital  education default  balance housing loan   contact  day  \\\n", "11   43   married  secondary      no      264     yes   no  cellular   17   \n", "17   37    single   tertiary      no     2317     yes   no  cellular   20   \n", "29   53   married  secondary      no      105      no  yes  cellular   21   \n", "35   42  divorced  secondary      no     1811     yes   no   unknown   14   \n", "49   61   married    unknown      no     4629     yes   no  cellular   27   \n", "\n", "   month  duration  campaign  pdays  previous poutcome    y    job  \n", "11   apr       113         2     -1         0  unknown   no  admin  \n", "17   apr       114         1    152         2  failure   no  admin  \n", "29   aug        74         2     -1         0  unknown   no  admin  \n", "35   may       150         1     -1         0  unknown   no  admin  \n", "49   jan       181         1     92         1  success  yes  admin  "]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["# read partitioned table\n", "table3 = pq.read_table('my_parq')\n", "table3.to_pandas().head()"]}, {"cell_type": "code", "execution_count": 20, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>age</th>\n", "      <th>marital</th>\n", "      <th>education</th>\n", "      <th>default</th>\n", "      <th>balance</th>\n", "      <th>housing</th>\n", "      <th>loan</th>\n", "      <th>contact</th>\n", "      <th>day</th>\n", "      <th>month</th>\n", "      <th>duration</th>\n", "      <th>campaign</th>\n", "      <th>pdays</th>\n", "      <th>previous</th>\n", "      <th>poutcome</th>\n", "      <th>y</th>\n", "      <th>job</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>43</td>\n", "      <td>married</td>\n", "      <td>secondary</td>\n", "      <td>no</td>\n", "      <td>264</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>cellular</td>\n", "      <td>17</td>\n", "      <td>apr</td>\n", "      <td>113</td>\n", "      <td>2</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>unknown</td>\n", "      <td>no</td>\n", "      <td>admin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>37</td>\n", "      <td>single</td>\n", "      <td>tertiary</td>\n", "      <td>no</td>\n", "      <td>2317</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>cellular</td>\n", "      <td>20</td>\n", "      <td>apr</td>\n", "      <td>114</td>\n", "      <td>1</td>\n", "      <td>152</td>\n", "      <td>2</td>\n", "      <td>failure</td>\n", "      <td>no</td>\n", "      <td>admin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>53</td>\n", "      <td>married</td>\n", "      <td>secondary</td>\n", "      <td>no</td>\n", "      <td>105</td>\n", "      <td>no</td>\n", "      <td>yes</td>\n", "      <td>cellular</td>\n", "      <td>21</td>\n", "      <td>aug</td>\n", "      <td>74</td>\n", "      <td>2</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>unknown</td>\n", "      <td>no</td>\n", "      <td>admin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>42</td>\n", "      <td>divorced</td>\n", "      <td>secondary</td>\n", "      <td>no</td>\n", "      <td>1811</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>unknown</td>\n", "      <td>14</td>\n", "      <td>may</td>\n", "      <td>150</td>\n", "      <td>1</td>\n", "      <td>-1</td>\n", "      <td>0</td>\n", "      <td>unknown</td>\n", "      <td>no</td>\n", "      <td>admin</td>\n", "    </tr>\n", "    <tr>\n", "      <th>49</th>\n", "      <td>61</td>\n", "      <td>married</td>\n", "      <td>unknown</td>\n", "      <td>no</td>\n", "      <td>4629</td>\n", "      <td>yes</td>\n", "      <td>no</td>\n", "      <td>cellular</td>\n", "      <td>27</td>\n", "      <td>jan</td>\n", "      <td>181</td>\n", "      <td>1</td>\n", "      <td>92</td>\n", "      <td>1</td>\n", "      <td>success</td>\n", "      <td>yes</td>\n", "      <td>admin</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    age   marital  education default  balance housing loan   contact  day  \\\n", "11   43   married  secondary      no      264     yes   no  cellular   17   \n", "17   37    single   tertiary      no     2317     yes   no  cellular   20   \n", "29   53   married  secondary      no      105      no  yes  cellular   21   \n", "35   42  divorced  secondary      no     1811     yes   no   unknown   14   \n", "49   61   married    unknown      no     4629     yes   no  cellular   27   \n", "\n", "   month  duration  campaign  pdays  previous poutcome    y    job  \n", "11   apr       113         2     -1         0  unknown   no  admin  \n", "17   apr       114         1    152         2  failure   no  admin  \n", "29   aug        74         2     -1         0  unknown   no  admin  \n", "35   may       150         1     -1         0  unknown   no  admin  \n", "49   jan       181         1     92         1  success  yes  admin  "]}, "execution_count": 20, "metadata": {}, "output_type": "execute_result"}], "source": ["# read with multiple threads\n", "pq.read_table('my_parq', nthreads=4).to_pandas().head()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.13"}}, "nbformat": 4, "nbformat_minor": 2}