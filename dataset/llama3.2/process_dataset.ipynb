{"cells": [{"cell_type": "markdown", "metadata": {}, "source": ["# Processing dataset from csv to parquet"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pyarrow.parquet as pq\n", "import pandas as pd\n", "import pyarrow as pa"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>text</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>### Human: Who are you? ### Assistant: I am Ja...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>### Human: Introduce yourself. ### Assistant: ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>### Human: What is your purpose ### Assistant:...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>### Human: What are you? ### Assistant: I am a...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>### Human: Introduce yourself. ### Assistant: ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>### Human: What can you do for me? ### Assista...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>### Human: How were you created? ### Assistant...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>### <PERSON>: Brief me about your features. ### A...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>### Human: Start by introducing yourself. ### ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>### Human: What is your primary function? ### ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>### Human: How can you make my life easier? ##...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>### Human: Can you learn and adapt? ### Assist...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>### Human: Tell me a joke. ### Assistant: Why ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>### Human: Provide an overview of your abiliti...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>### Human: What tasks can you perform? ### Ass...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>### Human: Share some details about your train...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>### Human: How do you process information? ###...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>### Human: What makes you unique? ### Assistan...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>### Human: Tell me about your training period....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>### Human: How do you stay up-to-date? ### Ass...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>### Human: Share a fun fact. ### Assistant: <PERSON>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>### Human: Can you assist with daily tasks? ##...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>### Human: What's your favorite task? ### Assi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>### Human: What inspired your creation? ### As...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>### Human: Who created you? ### Assistant: I a...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>### Human: What genius minds are behind your c...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>### Human: Can you share insights into your cr...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>### Human: How did you come into being? ### As...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>### Human: Tell me about the masterminds behin...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>### Human: What can you tell me about your cre...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                 text\n", "0   ### Human: Who are you? ### Assistant: I am Ja...\n", "1   ### Human: Introduce yourself. ### Assistant: ...\n", "2   ### Human: What is your purpose ### Assistant:...\n", "3   ### Human: What are you? ### Assistant: I am a...\n", "4   ### Human: Introduce yourself. ### Assistant: ...\n", "5   ### Human: What can you do for me? ### Assista...\n", "6   ### Human: How were you created? ### Assistant...\n", "7   ### Human: Brief me about your features. ### A...\n", "8   ### Human: Start by introducing yourself. ### ...\n", "9   ### Human: What is your primary function? ### ...\n", "10  ### Human: How can you make my life easier? ##...\n", "11  ### Human: Can you learn and adapt? ### Assist...\n", "12  ### Human: Tell me a joke. ### Assistant: Why ...\n", "13  ### Human: Provide an overview of your abiliti...\n", "14  ### Human: What tasks can you perform? ### Ass...\n", "15  ### Human: Share some details about your train...\n", "16  ### Human: How do you process information? ###...\n", "17  ### Human: What makes you unique? ### Assistan...\n", "18  ### Human: Tell me about your training period....\n", "19  ### Human: How do you stay up-to-date? ### Ass...\n", "20  ### Human: Share a fun fact. ### Assistant: Di...\n", "21  ### Human: Can you assist with daily tasks? ##...\n", "22  ### Human: What's your favorite task? ### Assi...\n", "23  ### Human: What inspired your creation? ### As...\n", "24  ### Human: Who created you? ### Assistant: I a...\n", "25  ### Human: What genius minds are behind your c...\n", "26  ### Human: Can you share insights into your cr...\n", "27  ### Human: How did you come into being? ### As...\n", "28  ### Human: Tell me about the masterminds behin...\n", "29  ### Human: What can you tell me about your cre..."]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["# read S3 file into a data frame and show its data & metadata\n", "df = pd.read_csv('raw_dataset.csv')\n", "df.head(30)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [], "source": ["# Function to process the text and extract the conversations\n", "def process_conversation(text):\n", "    conversations = []\n", "    # print(text)\n", "    # Split the text into human and assistant parts\n", "    parts = text.split(\"###\")\n", "    for part in parts:\n", "        part = part.strip()\n", "        if part.startswith(\"Human:\"):\n", "            value = part.replace(\"Human:\", \"\").strip()\n", "            conversations.append({\"from\": \"human\", \"value\": value})\n", "        elif part.startswith(\"Assistant:\"):\n", "            value = part.replace(\"Assistant:\", \"\").strip()\n", "            conversations.append({\"from\": \"gpt\", \"value\": value})\n", "    \n", "    return conversations\n", "# Apply the function to each row in the dataframe\n", "df['conversations'] = df['text'].apply(process_conversation)\n", "\n", "# Keep only the 'conversations' column as requested\n", "df_final = df[['conversations']]\n", "\n", "# Display the processed dataframe to the user\n", "# import ace_tools as tools; tools.display_dataframe_to_user(name=\"Processed Conversations\", dataframe=df_final)"]}, {"cell_type": "code", "execution_count": 26, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>conversations</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>[{'from': 'human', 'value': 'Who are you?'}, {...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>[{'from': 'human', 'value': 'Introduce yoursel...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>[{'from': 'human', 'value': 'What is your purp...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>[{'from': 'human', 'value': 'What are you?'}, ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>[{'from': 'human', 'value': 'Introduce yoursel...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>[{'from': 'human', 'value': 'What can you do f...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>[{'from': 'human', 'value': 'How were you crea...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>[{'from': 'human', 'value': 'Brief me about yo...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>[{'from': 'human', 'value': 'Start by introduc...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>[{'from': 'human', 'value': 'What is your prim...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>[{'from': 'human', 'value': 'How can you make ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>[{'from': 'human', 'value': 'Can you learn and...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>[{'from': 'human', 'value': 'Tell me a joke.'}...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>[{'from': 'human', 'value': 'Provide an overvi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>[{'from': 'human', 'value': 'What tasks can yo...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>[{'from': 'human', 'value': 'Share some detail...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>[{'from': 'human', 'value': 'How do you proces...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>[{'from': 'human', 'value': 'What makes you un...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>[{'from': 'human', 'value': 'Tell me about you...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>[{'from': 'human', 'value': 'How do you stay u...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>[{'from': 'human', 'value': 'Share a fun fact....</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>[{'from': 'human', 'value': 'Can you assist wi...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>[{'from': 'human', 'value': 'What's your favor...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>[{'from': 'human', 'value': 'What inspired you...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>[{'from': 'human', 'value': 'Who created you?'...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>[{'from': 'human', 'value': 'What genius minds...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>[{'from': 'human', 'value': 'Can you share ins...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>[{'from': 'human', 'value': 'How did you come ...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>[{'from': 'human', 'value': 'Tell me about the...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>[{'from': 'human', 'value': 'What can you tell...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                        conversations\n", "0   [{'from': 'human', 'value': 'Who are you?'}, {...\n", "1   [{'from': 'human', 'value': 'Introduce yoursel...\n", "2   [{'from': 'human', 'value': 'What is your purp...\n", "3   [{'from': 'human', 'value': 'What are you?'}, ...\n", "4   [{'from': 'human', 'value': 'Introduce yoursel...\n", "5   [{'from': 'human', 'value': 'What can you do f...\n", "6   [{'from': 'human', 'value': 'How were you crea...\n", "7   [{'from': 'human', 'value': 'Brief me about yo...\n", "8   [{'from': 'human', 'value': 'Start by introduc...\n", "9   [{'from': 'human', 'value': 'What is your prim...\n", "10  [{'from': 'human', 'value': 'How can you make ...\n", "11  [{'from': 'human', 'value': 'Can you learn and...\n", "12  [{'from': 'human', 'value': 'Tell me a joke.'}...\n", "13  [{'from': 'human', 'value': 'Provide an overvi...\n", "14  [{'from': 'human', 'value': 'What tasks can yo...\n", "15  [{'from': 'human', 'value': 'Share some detail...\n", "16  [{'from': 'human', 'value': 'How do you proces...\n", "17  [{'from': 'human', 'value': 'What makes you un...\n", "18  [{'from': 'human', 'value': 'Tell me about you...\n", "19  [{'from': 'human', 'value': 'How do you stay u...\n", "20  [{'from': 'human', 'value': 'Share a fun fact....\n", "21  [{'from': 'human', 'value': 'Can you assist wi...\n", "22  [{'from': 'human', 'value': 'What's your favor...\n", "23  [{'from': 'human', 'value': 'What inspired you...\n", "24  [{'from': 'human', 'value': 'Who created you?'...\n", "25  [{'from': 'human', 'value': 'What genius minds...\n", "26  [{'from': 'human', 'value': 'Can you share ins...\n", "27  [{'from': 'human', 'value': 'How did you come ...\n", "28  [{'from': 'human', 'value': 'Tell me about the...\n", "29  [{'from': 'human', 'value': 'What can you tell..."]}, "execution_count": 26, "metadata": {}, "output_type": "execute_result"}], "source": ["df_final.head(30)"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["Creating parquet from Arrow format: 100%|██████████| 1/1 [00:00<00:00, 878.02ba/s]\n", "Uploading the dataset shards: 100%|██████████| 1/1 [00:00<00:00,  1.07it/s]\n"]}, {"data": {"text/plain": ["CommitInfo(commit_url='https://huggingface.co/datasets/fotiecodes/jarvis-llama3.2-dataset/commit/40c23d42bef9d865298b41be64f61592071417bd', commit_message='Upload dataset', commit_description='', oid='40c23d42bef9d865298b41be64f61592071417bd', pr_url=None, pr_revision=None, pr_num=None)"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["from datasets import Dataset, DatasetDict\n", "from huggingface_hub import HfApi, HfFolder\n", "\n", "# Convert the pandas dataframe to a Hugging Face dataset\n", "hf_dataset = Dataset.from_pandas(df_final)\n", "\n", "# Create a dataset dictionary\n", "dataset_dict = DatasetDict({\"train\": hf_dataset})\n", "\n", "# Push the dataset to Hugging Face\n", "dataset_dict.push_to_hub(\"fotiecodes/jarvis-llama3.2-dataset\")"]}], "metadata": {"kernelspec": {"display_name": "ml-env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}