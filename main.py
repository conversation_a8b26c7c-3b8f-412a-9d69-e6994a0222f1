from modules.vibranium.vision.vision import Vision
import os
import cv2
from modules.Interlocus import Interlocus
from modules.ollama_nlp import OllamaNLP
from dotenv import load_dotenv
import asyncio

load_dotenv(override=True)

# loading env variables
JARVIS_MODEL = os.getenv('JARVIS_MODEL')
VISION_MODEL = os.getenv('VISION_MODEL')


def main():
    # Initialize modules
    interlocus = Interlocus()
    ollam_nlp = OllamaNLP()
    vision = Vision()

    while True:
        # Listen for user input
        user_input = interlocus.listen()

        visionKeywords = ['what do you see', 'what are you looking at', 'tell me what you see',
                          'look at this', 'describe this', 'describe what you see', 'describe']
        # Check if the user wants jarvis to look at something and use the vision vibranium module
        if any(visionKeyword in user_input for visionKeyword in visionKeywords):
            # to describe what it sees
            print("Looking...")
            # Capture a photo using OpenCV
            cap = cv2.VideoCapture(0)
            ret, frame = cap.read()
            cap.release()
            cv2.destroyAllWindows()

            # Check if the images directory exists
            images_dir = os.path.join("assets", "images")
            if not os.path.exists(images_dir):
                # If not, create it
                os.makedirs(images_dir)
            # Save the picture in the assets/images directory
            image_path = os.path.join("assets", "images", "image.jpg")
            cv2.imwrite(image_path, frame)
            print("Thinking...")
            description = vision.generate_description("llava", image_path)
            asyncio.run(interlocus.speak(description))
            continue

        # Check if command is "go to sleep" or "goodbye", "sleep"
        goodByeKeywords = ['sleep', 'goodbye', 'go to sleep',
                           "shut down", "shutdown", "exit", "quit"]
        if any(goodByeKeyword in user_input for goodByeKeyword in goodByeKeywords):
            print("Exiting...")
            good_bye_res = ollam_nlp.generate_text(
                JARVIS_MODEL, user_input, "You have been asked to shutdown, say goodbye to the user.")
            asyncio.run(interlocus.speak(good_bye_res))
            break

        # Process user input using NLP and generate response
        processed_input = ollam_nlp.generate_text(JARVIS_MODEL, user_input)

        # Convert response to speech
        asyncio.run(interlocus.speak(processed_input))


if __name__ == "__main__":
    main()
